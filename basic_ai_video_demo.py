#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Basic AI-Style Conversation Video Demo
Creates visual frames and audio for AI conversation video
Uses basic libraries only
"""

import os
import sys
from PIL import Image, ImageDraw, ImageFont
import numpy as np
from datetime import datetime
import pyttsx3
import time

class BasicAIVideoDemo:
    """Basic AI Conversation Video Demo Generator"""
    
    def __init__(self):
        # Video settings for YouTube Shorts (9:16 aspect ratio)
        self.width = 1080
        self.height = 1920
        self.fps = 30
        
        # Colors and styling
        self.bg_colors = [
            (20, 25, 40),    # Dark blue
            (25, 20, 40),    # Dark purple
            (40, 20, 25),    # Dark red
            (20, 40, 25),    # Dark green
        ]
        
        # Character settings
        self.characters = {
            'person1': {
                'name': 'أحمد',
                'voice_rate': 180,
                'voice_volume': 0.9,
                'color': (100, 200, 255),  # Light blue
                'position': 'left'
            },
            'person2': {
                'name': 'سارة',
                'voice_rate': 200,
                'voice_volume': 0.8,
                'color': (255, 150, 200),  # Light pink
                'position': 'right'
            }
        }
        
        # Conversation data
        self.conversation = [
            {
                'speaker': 'person1',
                'text': 'سارة، شفتي آخر فيديو للذكاء الاصطناعي؟',
                'duration': 3.5
            },
            {
                'speaker': 'person2', 
                'text': 'أي واحد؟ كلهم يقولون نفس الشي!',
                'duration': 3.0
            },
            {
                'speaker': 'person1',
                'text': 'هذا اللي يقول الذكاء الاصطناعي راح يحل كل مشاكل العالم',
                'duration': 4.0
            },
            {
                'speaker': 'person2',
                'text': 'وأنا ما أقدر أخلي الذكاء الاصطناعي يفهم إني أريد قهوة بدون سكر!',
                'duration': 4.5
            },
            {
                'speaker': 'person1',
                'text': 'هههه صحيح! يقولك راح نطير للمريخ',
                'duration': 3.5
            },
            {
                'speaker': 'person2',
                'text': 'وأنا أريد أطير لأقرب مقهى بس!',
                'duration': 3.0
            },
            {
                'speaker': 'person1',
                'text': 'المستقبل مخيف... أو مضحك، ما أدري!',
                'duration': 3.5
            },
            {
                'speaker': 'person2',
                'text': 'المهم نضحك قبل ما الروبوتات تاخذ شغلنا!',
                'duration': 4.0
            }
        ]
        
        # Initialize TTS engine
        try:
            self.tts_engine = pyttsx3.init()
            self.setup_tts()
        except:
            print("⚠️ TTS engine not available, will create visual frames only")
            self.tts_engine = None
    
    def setup_tts(self):
        """Setup text-to-speech engine"""
        if not self.tts_engine:
            return
        try:
            # Get available voices
            voices = self.tts_engine.getProperty('voices')
            if voices:
                # Try to find Arabic voice or use default
                for voice in voices:
                    if 'arabic' in voice.name.lower() or 'ar' in voice.id.lower():
                        self.tts_engine.setProperty('voice', voice.id)
                        break
        except:
            print("⚠️ Using default TTS voice")
    
    def create_gradient_background(self, frame_number):
        """Create animated gradient background"""
        # Create base image
        img = Image.new('RGB', (self.width, self.height), (0, 0, 0))
        
        # Calculate animation progress
        progress = (frame_number % 60) / 60.0  # 2-second cycle at 30fps
        
        # Select colors based on progress
        color1_idx = int(progress * len(self.bg_colors)) % len(self.bg_colors)
        color2_idx = (color1_idx + 1) % len(self.bg_colors)
        
        color1 = self.bg_colors[color1_idx]
        color2 = self.bg_colors[color2_idx]
        
        # Create gradient
        for y in range(self.height):
            ratio = y / self.height
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
            
            # Draw horizontal line
            draw = ImageDraw.Draw(img)
            draw.line([(0, y), (self.width, y)], fill=(r, g, b))
        
        return img
    
    def add_floating_particles(self, img, frame_number):
        """Add floating particles to the background"""
        draw = ImageDraw.Draw(img)
        
        # Create particles
        num_particles = 15
        for i in range(num_particles):
            # Calculate particle position with animation
            x = int((np.sin(frame_number * 0.02 + i) * 0.3 + 0.5) * self.width)
            y = int((np.cos(frame_number * 0.015 + i * 0.5) * 0.4 + 0.5) * self.height)
            size = int(3 + 2 * np.sin(frame_number * 0.03 + i))
            
            # Draw particle
            if 0 <= x < self.width and 0 <= y < self.height and size > 0:
                draw.ellipse([x-size, y-size, x+size, y+size], fill=(255, 255, 255, 100))
        
        return img
    
    def create_text_frame(self, text, character, frame_number, total_frames):
        """Create a frame with text for the character"""
        # Create background
        img = self.create_gradient_background(frame_number)
        img = self.add_floating_particles(img, frame_number)
        
        # Character settings
        char_info = self.characters[character]
        color = char_info['color']
        position = char_info['position']
        name = char_info['name']
        
        # Try to load font
        try:
            font_size = 45
            font = ImageFont.truetype("arial.ttf", font_size)
            name_font = ImageFont.truetype("arial.ttf", 30)
        except:
            font = ImageFont.load_default()
            name_font = ImageFont.load_default()
        
        draw = ImageDraw.Draw(img)
        
        # Calculate typing animation
        typing_progress = frame_number / total_frames
        chars_to_show = int(typing_progress * len(text))
        display_text = text[:chars_to_show]
        
        # Text positioning
        text_y = self.height // 2 - 100
        name_y = self.height - 300
        
        if position == 'left':
            text_x = 50
            name_x = 50
        else:
            # Calculate text width for right alignment
            bbox = draw.textbbox((0, 0), display_text, font=font)
            text_width = bbox[2] - bbox[0]
            text_x = self.width - text_width - 50
            name_x = self.width - 200
        
        # Draw text with outline
        outline_color = (0, 0, 0)
        for dx in [-2, -1, 0, 1, 2]:
            for dy in [-2, -1, 0, 1, 2]:
                if dx != 0 or dy != 0:
                    draw.text((text_x + dx, text_y + dy), display_text, font=font, fill=outline_color)
        
        # Draw main text
        draw.text((text_x, text_y), display_text, font=font, fill=color)
        
        # Draw character name indicator
        name_bg = Image.new('RGBA', (200, 50), (0, 0, 0, 180))
        name_draw = ImageDraw.Draw(name_bg)
        name_draw.text((10, 10), name, font=name_font, fill=color)
        
        # Paste name indicator
        img.paste(name_bg, (name_x, name_y), name_bg)
        
        return img
    
    def create_title_frame(self):
        """Create title frame"""
        img = self.create_gradient_background(0)
        draw = ImageDraw.Draw(img)
        
        title_text = "محادثة ذكية 🤖"
        subtitle_text = "بين الواقع والخيال"
        
        try:
            title_font = ImageFont.truetype("arial.ttf", 80)
            subtitle_font = ImageFont.truetype("arial.ttf", 40)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
        
        # Get text dimensions
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        
        subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        
        # Center positions
        title_x = (self.width - title_width) // 2
        title_y = self.height // 2 - 100
        subtitle_x = (self.width - subtitle_width) // 2
        subtitle_y = title_y + 100
        
        # Draw title with glow effect
        glow_color = (255, 255, 0)
        text_color = (255, 255, 255)
        
        # Glow effect
        for offset in range(1, 5):
            draw.text((title_x + offset, title_y + offset), title_text, 
                     font=title_font, fill=glow_color)
            draw.text((title_x - offset, title_y - offset), title_text, 
                     font=title_font, fill=glow_color)
        
        # Main title
        draw.text((title_x, title_y), title_text, font=title_font, fill=text_color)
        draw.text((subtitle_x, subtitle_y), subtitle_text, font=subtitle_font, 
                 fill=(200, 200, 200))
        
        return img
    
    def create_outro_frame(self):
        """Create outro frame"""
        img = self.create_gradient_background(30)
        draw = ImageDraw.Draw(img)
        
        outro_text = "شكراً للمشاهدة! 👍"
        cta_text = "اشترك للمزيد من المحتوى المسلي"
        
        try:
            outro_font = ImageFont.truetype("arial.ttf", 60)
            cta_font = ImageFont.truetype("arial.ttf", 35)
        except:
            outro_font = ImageFont.load_default()
            cta_font = ImageFont.load_default()
        
        # Get text dimensions and center
        outro_bbox = draw.textbbox((0, 0), outro_text, font=outro_font)
        outro_width = outro_bbox[2] - outro_bbox[0]
        outro_x = (self.width - outro_width) // 2
        outro_y = self.height // 2 - 50
        
        cta_bbox = draw.textbbox((0, 0), cta_text, font=cta_font)
        cta_width = cta_bbox[2] - cta_bbox[0]
        cta_x = (self.width - cta_width) // 2
        cta_y = outro_y + 80
        
        # Draw text
        draw.text((outro_x, outro_y), outro_text, font=outro_font, 
                 fill=(255, 255, 255))
        draw.text((cta_x, cta_y), cta_text, font=cta_font, 
                 fill=(255, 200, 100))
        
        return img
    
    def generate_audio(self, text, character, filename):
        """Generate audio for text"""
        if not self.tts_engine:
            print(f"⚠️ TTS not available, skipping audio for: {text[:30]}...")
            return False
        
        try:
            # Set voice properties for character
            char_info = self.characters[character]
            self.tts_engine.setProperty('rate', char_info['voice_rate'])
            self.tts_engine.setProperty('volume', char_info['voice_volume'])
            
            # Generate audio
            self.tts_engine.save_to_file(text, filename)
            self.tts_engine.runAndWait()
            
            print(f"✅ Generated audio: {filename}")
            return True
        except Exception as e:
            print(f"❌ Error generating audio: {e}")
            return False
    
    def create_demo_frames(self):
        """Create demo frames for the AI conversation video"""
        print("🎬 Creating AI Conversation Video Demo Frames...")
        
        frames_dir = f"ai_video_frames_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(frames_dir, exist_ok=True)
        
        frame_count = 0
        
        # Create title frames (2 seconds = 60 frames at 30fps)
        print("🎨 Creating title frames...")
        title_img = self.create_title_frame()
        for i in range(60):
            filename = os.path.join(frames_dir, f"frame_{frame_count:04d}.png")
            title_img.save(filename)
            frame_count += 1
        
        # Create conversation frames
        for conv_idx, conv in enumerate(self.conversation):
            speaker = conv['speaker']
            text = conv['text']
            duration = conv['duration']
            
            print(f"🎤 Creating frames for: {speaker} - {text[:30]}...")
            
            # Calculate frames for this conversation part
            frames_for_this = int(duration * self.fps)
            
            # Generate audio
            audio_filename = os.path.join(frames_dir, f"audio_{conv_idx}_{speaker}.wav")
            self.generate_audio(text, speaker, audio_filename)
            
            # Create frames
            for frame_idx in range(frames_for_this):
                img = self.create_text_frame(text, speaker, frame_idx, frames_for_this)
                filename = os.path.join(frames_dir, f"frame_{frame_count:04d}.png")
                img.save(filename)
                frame_count += 1
        
        # Create outro frames (2 seconds = 60 frames)
        print("🎨 Creating outro frames...")
        outro_img = self.create_outro_frame()
        for i in range(60):
            filename = os.path.join(frames_dir, f"frame_{frame_count:04d}.png")
            outro_img.save(filename)
            frame_count += 1
        
        print(f"\n🎉 Demo frames created successfully!")
        print(f"📁 Frames directory: {frames_dir}")
        print(f"📊 Total frames: {frame_count}")
        print(f"⏱️ Video duration: {frame_count / self.fps:.1f} seconds")
        
        # Create assembly instructions
        instructions_file = os.path.join(frames_dir, "assembly_instructions.txt")
        with open(instructions_file, 'w', encoding='utf-8') as f:
            f.write("🎬 AI Conversation Video Assembly Instructions\n")
            f.write("=" * 50 + "\n\n")
            f.write("📁 This folder contains:\n")
            f.write(f"• {frame_count} video frames (frame_0000.png to frame_{frame_count-1:04d}.png)\n")
            f.write("• Audio files for each conversation part\n")
            f.write("• This instruction file\n\n")
            f.write("🎥 To create the final video:\n")
            f.write("1. Use video editing software (e.g., FFmpeg, Adobe Premiere, DaVinci Resolve)\n")
            f.write("2. Import all frames as an image sequence\n")
            f.write("3. Set frame rate to 30 FPS\n")
            f.write("4. Add audio files at appropriate timestamps\n")
            f.write("5. Export as MP4 (H.264) with 1080x1920 resolution\n\n")
            f.write("🔧 FFmpeg command example:\n")
            f.write("ffmpeg -framerate 30 -i frame_%04d.png -c:v libx264 -pix_fmt yuv420p output.mp4\n\n")
            f.write("📱 Perfect for:\n")
            f.write("• YouTube Shorts\n")
            f.write("• TikTok\n")
            f.write("• Instagram Reels\n")
            f.write("• Facebook Stories\n")
        
        return frames_dir


def main():
    """Main function to create the AI conversation video demo"""
    try:
        print("🤖 AI Conversation Video Demo Generator")
        print("=" * 50)
        
        # Create demo generator
        demo = BasicAIVideoDemo()
        
        # Generate frames and audio
        frames_dir = demo.create_demo_frames()
        
        print(f"\n✅ Demo creation completed successfully!")
        print(f"📁 Output directory: {frames_dir}")
        print("\n🎯 Next steps:")
        print("1. Review the generated frames")
        print("2. Use video editing software to combine frames and audio")
        print("3. Export as MP4 for social media")
        print("\n📱 Perfect for YouTube Shorts, TikTok, Instagram Reels!")
        
    except Exception as e:
        print(f"❌ Error creating demo: {str(e)}")
        print("\nPlease ensure you have the required libraries:")
        print("pip install pillow pyttsx3 numpy")


if __name__ == "__main__":
    main()
