#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick AI-Style Conversation Video Sample
Creates a few sample frames to demonstrate the concept
"""

import os
from PIL import Image, ImageDraw, ImageFont
import numpy as np
from datetime import datetime

class QuickAIVideoSample:
    """Quick AI Video Sample Generator"""
    
    def __init__(self):
        self.width = 1080
        self.height = 1920
        
        # Character settings
        self.characters = {
            'أحمد': {
                'color': (100, 200, 255),  # Light blue
                'position': 'left'
            },
            'سارة': {
                'color': (255, 150, 200),  # Light pink
                'position': 'right'
            }
        }
        
        # Sample conversation
        self.sample_conversation = [
            {
                'speaker': 'أحمد',
                'text': 'سارة، شفتي آخر فيديو للذكاء الاصطناعي؟'
            },
            {
                'speaker': 'سارة',
                'text': 'أي واحد؟ كلهم يقولون نفس الشي!'
            },
            {
                'speaker': 'أحمد',
                'text': 'هذا اللي يقول الذكاء الاصطناعي راح يحل كل مشاكل العالم'
            },
            {
                'speaker': 'سارة',
                'text': 'وأنا ما أقدر أخلي الذكاء الاصطناعي يفهم إني أريد قهوة بدون سكر!'
            }
        ]
    
    def create_gradient_background(self, color_index=0):
        """Create gradient background"""
        colors = [
            [(20, 25, 40), (40, 45, 80)],    # Dark blue gradient
            [(25, 20, 40), (50, 40, 80)],    # Dark purple gradient
            [(40, 20, 25), (80, 40, 50)],    # Dark red gradient
            [(20, 40, 25), (40, 80, 50)],    # Dark green gradient
        ]
        
        color1, color2 = colors[color_index % len(colors)]
        
        img = Image.new('RGB', (self.width, self.height))
        draw = ImageDraw.Draw(img)
        
        for y in range(self.height):
            ratio = y / self.height
            r = int(color1[0] * (1 - ratio) + color2[0] * ratio)
            g = int(color1[1] * (1 - ratio) + color2[1] * ratio)
            b = int(color1[2] * (1 - ratio) + color2[2] * ratio)
            draw.line([(0, y), (self.width, y)], fill=(r, g, b))
        
        return img
    
    def add_particles(self, img, seed=0):
        """Add floating particles"""
        draw = ImageDraw.Draw(img)
        np.random.seed(seed)
        
        for i in range(10):
            x = np.random.randint(0, self.width)
            y = np.random.randint(0, self.height)
            size = np.random.randint(2, 6)
            draw.ellipse([x-size, y-size, x+size, y+size], fill=(255, 255, 255, 150))
        
        return img
    
    def create_sample_frame(self, speaker, text, frame_type="conversation"):
        """Create a sample frame"""
        # Create background
        color_index = 0 if speaker == 'أحمد' else 1
        img = self.create_gradient_background(color_index)
        img = self.add_particles(img, hash(speaker) % 100)
        
        draw = ImageDraw.Draw(img)
        
        # Try to load font
        try:
            font = ImageFont.truetype("arial.ttf", 50)
            name_font = ImageFont.truetype("arial.ttf", 35)
        except:
            font = ImageFont.load_default()
            name_font = ImageFont.load_default()
        
        # Character settings
        char_info = self.characters[speaker]
        color = char_info['color']
        position = char_info['position']
        
        # Text positioning
        text_y = self.height // 2 - 100
        name_y = self.height - 300
        
        # Wrap text for better display
        words = text.split()
        lines = []
        current_line = ""
        
        for word in words:
            test_line = current_line + " " + word if current_line else word
            bbox = draw.textbbox((0, 0), test_line, font=font)
            if bbox[2] - bbox[0] < self.width - 100:
                current_line = test_line
            else:
                if current_line:
                    lines.append(current_line)
                current_line = word
        if current_line:
            lines.append(current_line)
        
        # Draw text lines
        for i, line in enumerate(lines):
            line_y = text_y + i * 60
            
            if position == 'left':
                text_x = 50
            else:
                bbox = draw.textbbox((0, 0), line, font=font)
                text_width = bbox[2] - bbox[0]
                text_x = self.width - text_width - 50
            
            # Draw text with outline
            for dx in [-2, -1, 0, 1, 2]:
                for dy in [-2, -1, 0, 1, 2]:
                    if dx != 0 or dy != 0:
                        draw.text((text_x + dx, line_y + dy), line, font=font, fill=(0, 0, 0))
            
            # Draw main text
            draw.text((text_x, line_y), line, font=font, fill=color)
        
        # Draw character name
        name_x = 50 if position == 'left' else self.width - 200
        
        # Name background
        name_bg_coords = [name_x, name_y, name_x + 180, name_y + 50]
        draw.rectangle(name_bg_coords, fill=(0, 0, 0, 180), outline=color, width=2)
        
        # Name text
        draw.text((name_x + 10, name_y + 10), speaker, font=name_font, fill=color)
        
        return img
    
    def create_title_frame(self):
        """Create title frame"""
        img = self.create_gradient_background(2)
        img = self.add_particles(img, 42)
        
        draw = ImageDraw.Draw(img)
        
        title_text = "محادثة ذكية 🤖"
        subtitle_text = "بين الواقع والخيال"
        
        try:
            title_font = ImageFont.truetype("arial.ttf", 90)
            subtitle_font = ImageFont.truetype("arial.ttf", 45)
        except:
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
        
        # Center title
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        title_x = (self.width - title_width) // 2
        title_y = self.height // 2 - 100
        
        # Center subtitle
        subtitle_bbox = draw.textbbox((0, 0), subtitle_text, font=subtitle_font)
        subtitle_width = subtitle_bbox[2] - subtitle_bbox[0]
        subtitle_x = (self.width - subtitle_width) // 2
        subtitle_y = title_y + 120
        
        # Draw title with glow
        glow_color = (255, 255, 0)
        for offset in range(1, 4):
            draw.text((title_x + offset, title_y + offset), title_text, 
                     font=title_font, fill=glow_color)
        
        draw.text((title_x, title_y), title_text, font=title_font, fill=(255, 255, 255))
        draw.text((subtitle_x, subtitle_y), subtitle_text, font=subtitle_font, 
                 fill=(200, 200, 200))
        
        return img
    
    def create_outro_frame(self):
        """Create outro frame"""
        img = self.create_gradient_background(3)
        img = self.add_particles(img, 99)
        
        draw = ImageDraw.Draw(img)
        
        outro_text = "شكراً للمشاهدة! 👍"
        cta_text = "اشترك للمزيد من المحتوى المسلي"
        
        try:
            outro_font = ImageFont.truetype("arial.ttf", 65)
            cta_font = ImageFont.truetype("arial.ttf", 40)
        except:
            outro_font = ImageFont.load_default()
            cta_font = ImageFont.load_default()
        
        # Center outro text
        outro_bbox = draw.textbbox((0, 0), outro_text, font=outro_font)
        outro_width = outro_bbox[2] - outro_bbox[0]
        outro_x = (self.width - outro_width) // 2
        outro_y = self.height // 2 - 50
        
        # Center CTA text
        cta_bbox = draw.textbbox((0, 0), cta_text, font=cta_font)
        cta_width = cta_bbox[2] - cta_bbox[0]
        cta_x = (self.width - cta_width) // 2
        cta_y = outro_y + 100
        
        draw.text((outro_x, outro_y), outro_text, font=outro_font, fill=(255, 255, 255))
        draw.text((cta_x, cta_y), cta_text, font=cta_font, fill=(255, 200, 100))
        
        return img
    
    def create_sample_video_frames(self):
        """Create sample video frames"""
        print("🎬 Creating AI Conversation Video Sample Frames...")
        
        # Create output directory
        output_dir = f"ai_video_sample_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        os.makedirs(output_dir, exist_ok=True)
        
        frames = []
        
        # Title frame
        print("🎨 Creating title frame...")
        title_frame = self.create_title_frame()
        title_path = os.path.join(output_dir, "01_title.png")
        title_frame.save(title_path)
        frames.append(("Title", title_path))
        
        # Conversation frames
        for i, conv in enumerate(self.sample_conversation):
            speaker = conv['speaker']
            text = conv['text']
            
            print(f"🎤 Creating frame for {speaker}: {text[:30]}...")
            
            frame = self.create_sample_frame(speaker, text)
            frame_path = os.path.join(output_dir, f"{i+2:02d}_conversation_{speaker}.png")
            frame.save(frame_path)
            frames.append((f"{speaker}: {text[:30]}...", frame_path))
        
        # Outro frame
        print("🎨 Creating outro frame...")
        outro_frame = self.create_outro_frame()
        outro_path = os.path.join(output_dir, f"{len(self.sample_conversation)+2:02d}_outro.png")
        outro_frame.save(outro_path)
        frames.append(("Outro", outro_path))
        
        # Create summary
        summary_path = os.path.join(output_dir, "video_summary.txt")
        with open(summary_path, 'w', encoding='utf-8') as f:
            f.write("🎬 AI Conversation Video Sample\n")
            f.write("=" * 40 + "\n\n")
            f.write("📱 Video Specifications:\n")
            f.write("• Resolution: 1080x1920 (9:16 for YouTube Shorts)\n")
            f.write("• Format: Vertical video for social media\n")
            f.write("• Style: AI conversation with animated backgrounds\n\n")
            
            f.write("🎭 Characters:\n")
            f.write("• أحمد - Blue text, left side\n")
            f.write("• سارة - Pink text, right side\n\n")
            
            f.write("📝 Conversation Topic:\n")
            f.write("Humorous discussion about AI expectations vs reality\n\n")
            
            f.write("🖼️ Generated Frames:\n")
            for i, (desc, path) in enumerate(frames, 1):
                f.write(f"{i}. {desc}\n")
            
            f.write("\n🎯 Usage:\n")
            f.write("• Perfect for YouTube Shorts\n")
            f.write("• TikTok videos\n")
            f.write("• Instagram Reels\n")
            f.write("• Facebook Stories\n\n")
            
            f.write("🔧 To create video:\n")
            f.write("1. Import frames into video editing software\n")
            f.write("2. Set each frame duration (3-4 seconds)\n")
            f.write("3. Add text-to-speech audio\n")
            f.write("4. Add background music (optional)\n")
            f.write("5. Export as MP4\n")
        
        print(f"\n🎉 Sample frames created successfully!")
        print(f"📁 Output directory: {output_dir}")
        print(f"📊 Total frames: {len(frames)}")
        print("\n🎯 Perfect for social media content!")
        
        return output_dir, frames


def main():
    """Main function"""
    try:
        print("🤖 Quick AI Conversation Video Sample Generator")
        print("=" * 50)
        
        # Create sample generator
        generator = QuickAIVideoSample()
        
        # Generate sample frames
        output_dir, frames = generator.create_sample_video_frames()
        
        print(f"\n✅ Sample creation completed!")
        print(f"📁 Check the '{output_dir}' folder for:")
        print("• Sample video frames")
        print("• Video summary and instructions")
        
        print(f"\n📱 Ready for:")
        print("• YouTube Shorts")
        print("• TikTok")
        print("• Instagram Reels")
        print("• Facebook Stories")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")


if __name__ == "__main__":
    main()
